export const pipelineData = {
  id: 'demo-pipeline',
  name: 'Demo流水线项目',
  status: 'success',
  lastRun: '2024-04-14 16:15',
  duration: '12分钟',
  stages: [
    {
      id: 'stage-1',
      name: '测试',
      width: 300,
      tasks: [
        {
          id: 'task-1',
          name: 'Java代码扫描',
          type: 'java-code-scan',
          status: 'success',
          parallel: true,
          parallelGroup: 1
        },
        {
          id: 'task-2',
          name: 'Java安全扫描',
          type: 'java-security-scan',
          status: 'success',
          parallel: true,
          parallelGroup: 1
        }
      ]
    },
    {
      id: 'stage-2',
      name: '构建',
      width: 300,
      tasks: [
        {
          id: 'task-3',
          name: 'Java构建',
          type: 'maven-build',
          status: 'success',
          parallel: false
        }
      ]
    },
    {
      id: 'stage-3',
      name: '部署',
      width: 300,
      tasks: [
        {
          id: 'task-4',
          name: 'Maven部署',
          type: 'k8s-deploy',
          status: 'running',
          parallel: false
        }
      ]
    }
  ],
  connections: [
    { from: 'task-1', to: 'task-3', type: 'parallel-to-serial' },
    { from: 'task-2', to: 'task-3', type: 'parallel-to-serial' },
    { from: 'task-3', to: 'task-4', type: 'serial' }
  ]
}

export const taskTypes = [
  {
    category: '代码质量',
    tasks: [
      { id: 'java-code-scan', name: 'Java代码扫描', icon: '🔍' },
      { id: 'java-security-scan', name: 'Java安全扫描', icon: '🔒' },
      { id: 'python-code-scan', name: 'Python代码扫描', icon: '🐍' },
      { id: 'javascript-scan', name: 'JavaScript代码扫描', icon: '📜' }
    ]
  },
  {
    category: '构建编译',
    tasks: [
      { id: 'maven-build', name: 'Maven构建', icon: '🔨' },
      { id: 'gradle-build', name: 'Gradle构建', icon: '⚙️' },
      { id: 'npm-build', name: 'NPM构建', icon: '📦' },
      { id: 'docker-build', name: 'Docker构建', icon: '🐳' }
    ]
  },
  {
    category: '测试',
    tasks: [
      { id: 'unit-test', name: '单元测试', icon: '🧪' },
      { id: 'integration-test', name: '集成测试', icon: '🔗' },
      { id: 'e2e-test', name: 'E2E测试', icon: '🎯' }
    ]
  },
  {
    category: '部署',
    tasks: [
      { id: 'k8s-deploy', name: 'Kubernetes部署', icon: '☸️' },
      { id: 'docker-deploy', name: 'Docker部署', icon: '🚀' },
      { id: 'ftp-deploy', name: 'FTP部署', icon: '📤' }
    ]
  }
]
