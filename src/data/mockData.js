export const pipelineData = {
  id: 'demo-pipeline',
  name: 'Demo流水线项目',
  status: 'success',
  lastRun: '2024-04-14 16:15',
  duration: '12分钟',
  stages: [
    {
      id: 'stage-1',
      name: '测试',
      position: { x: 100, y: 200 },
      tasks: [
        {
          id: 'task-1',
          name: 'Java代码扫描',
          type: 'code-scan',
          status: 'success',
          position: { x: 150, y: 150 }
        },
        {
          id: 'task-2', 
          name: 'Java安全扫描',
          type: 'security-scan',
          status: 'success',
          position: { x: 150, y: 250 }
        }
      ]
    },
    {
      id: 'stage-2',
      name: '构建',
      position: { x: 400, y: 200 },
      tasks: [
        {
          id: 'task-3',
          name: 'Java构建',
          type: 'build',
          status: 'success',
          position: { x: 450, y: 200 }
        }
      ]
    },
    {
      id: 'stage-3',
      name: '部署',
      position: { x: 700, y: 200 },
      tasks: [
        {
          id: 'task-4',
          name: '<PERSON><PERSON>部署',
          type: 'deploy',
          status: 'running',
          position: { x: 750, y: 200 }
        }
      ]
    }
  ],
  connections: [
    { from: 'task-1', to: 'task-3' },
    { from: 'task-2', to: 'task-3' },
    { from: 'task-3', to: 'task-4' }
  ]
}

export const taskTypes = [
  {
    category: '代码质量',
    tasks: [
      { id: 'java-code-scan', name: 'Java代码扫描', icon: '🔍' },
      { id: 'java-security-scan', name: 'Java安全扫描', icon: '🔒' },
      { id: 'python-code-scan', name: 'Python代码扫描', icon: '🐍' },
      { id: 'javascript-scan', name: 'JavaScript代码扫描', icon: '📜' }
    ]
  },
  {
    category: '构建编译',
    tasks: [
      { id: 'maven-build', name: 'Maven构建', icon: '🔨' },
      { id: 'gradle-build', name: 'Gradle构建', icon: '⚙️' },
      { id: 'npm-build', name: 'NPM构建', icon: '📦' },
      { id: 'docker-build', name: 'Docker构建', icon: '🐳' }
    ]
  },
  {
    category: '测试',
    tasks: [
      { id: 'unit-test', name: '单元测试', icon: '🧪' },
      { id: 'integration-test', name: '集成测试', icon: '🔗' },
      { id: 'e2e-test', name: 'E2E测试', icon: '🎯' }
    ]
  },
  {
    category: '部署',
    tasks: [
      { id: 'k8s-deploy', name: 'Kubernetes部署', icon: '☸️' },
      { id: 'docker-deploy', name: 'Docker部署', icon: '🚀' },
      { id: 'ftp-deploy', name: 'FTP部署', icon: '📤' }
    ]
  }
]
