<template>
  <div
    class="stage-node"
    :style="stageStyle"
    @mouseenter="$emit('mouseenter', stage)"
    @mouseleave="$emit('mouseleave')"
  >
    <div class="stage-header">
      <h3 class="stage-title">{{ stage.name }}</h3>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StageNode',
  props: {
    stage: {
      type: Object,
      required: true
    }
  },
  emits: ['mouseenter', 'mouseleave'],
  computed: {
    stageStyle() {
      return {
        position: 'absolute',
        left: `${this.stage.position.x}px`,
        top: `${this.stage.position.y - 40}px`
      }
    }
  }
}
</script>

<style scoped>
.stage-node {
  background: #e6f7ff;
  border: 2px solid #91d5ff;
  border-radius: 8px;
  padding: 8px 16px;
  min-width: 80px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 5;
}

.stage-node:hover {
  background: #bae7ff;
  border-color: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.stage-title {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
  margin: 0;
}
</style>
