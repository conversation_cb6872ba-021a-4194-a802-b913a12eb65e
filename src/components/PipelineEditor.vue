<template>
  <div class="pipeline-editor">
    <!-- 头部导航 -->
    <header class="editor-header">
      <div class="header-content">
        <div class="breadcrumb">
          <button class="btn-back" @click="goBack">←</button>
          <span class="breadcrumb-item">Demo流水线项目</span>
        </div>
        <div class="header-actions">
          <button class="btn btn-secondary">上一步</button>
          <button class="btn btn-secondary">运行流水线</button>
          <button class="btn btn-secondary">变更记录</button>
          <button class="btn btn-primary">保存并运行</button>
        </div>
      </div>
    </header>

    <!-- 编辑器主体 -->
    <div class="editor-main">
      <!-- 左侧工具栏 -->
      <div class="left-sidebar">
        <div class="sidebar-content">
          <div class="project-info">
            <div class="project-icon">C</div>
            <div class="project-details">
              <div class="project-name">master</div>
              <div class="project-id">60e41596a6b6e27b3d...</div>
            </div>
          </div>
          <button class="btn btn-secondary add-stage-btn">+ 添加流水线阶段</button>
        </div>
      </div>

      <!-- 画布区域 -->
      <div class="canvas-area" ref="canvas">
        <svg class="pipeline-canvas" :width="canvasWidth" :height="canvasHeight">
          <!-- 连接线 -->
          <g class="connections">
            <path
              v-for="connection in connections"
              :key="`${connection.from}-${connection.to}`"
              :d="getConnectionPath(connection)"
              class="connection-line"
              @mouseenter="showAddSerialButton($event, connection)"
              @mouseleave="hideAddSerialButton"
            />
          </g>
        </svg>

        <!-- 阶段和任务节点 -->
        <div class="nodes-container">
          <!-- 阶段节点 -->
          <StageNode
            v-for="stage in pipelineData.stages"
            :key="stage.id"
            :stage="stage"
            @mouseenter="showAddParallelButton(stage)"
            @mouseleave="hideAddParallelButton"
          />

          <!-- 任务节点 -->
          <TaskNode
            v-for="task in allTasks"
            :key="task.id"
            :task="task"
            @click="editTask(task)"
          />

          <!-- 添加并行任务按钮 -->
          <button
            v-if="showParallelButton"
            class="add-button add-parallel"
            :style="parallelButtonStyle"
            @click="addParallelTask"
          >
            +
          </button>

          <!-- 添加串行任务按钮 -->
          <button
            v-if="showSerialButton"
            class="add-button add-serial"
            :style="serialButtonStyle"
            @click="addSerialTask"
          >
            +
          </button>
        </div>
      </div>

      <!-- 任务选择器侧边栏 -->
      <TaskSelector
        :visible="showTaskSelector"
        @close="closeTaskSelector"
        @select="selectTask"
      />

      <!-- 任务配置模态框 -->
      <TaskConfigModal
        :visible="showTaskConfig"
        :task="selectedTask"
        @close="closeTaskConfig"
        @save="saveTaskConfig"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { pipelineData } from '../data/mockData.js'
import StageNode from './StageNode.vue'
import TaskNode from './TaskNode.vue'
import TaskSelector from './TaskSelector.vue'
import TaskConfigModal from './TaskConfigModal.vue'

export default {
  name: 'PipelineEditor',
  components: {
    StageNode,
    TaskNode,
    TaskSelector,
    TaskConfigModal
  },
  setup() {
    const router = useRouter()
    const canvas = ref(null)
    
    // 响应式数据
    const canvasWidth = ref(1200)
    const canvasHeight = ref(800)
    const showParallelButton = ref(false)
    const showSerialButton = ref(false)
    const showTaskSelector = ref(false)
    const showTaskConfig = ref(false)
    const selectedTask = ref(null)
    const selectedStage = ref(null)
    const parallelButtonStyle = ref({})
    const serialButtonStyle = ref({})

    // 计算属性
    const allTasks = computed(() => {
      return pipelineData.stages.flatMap(stage => stage.tasks)
    })

    const connections = computed(() => {
      return pipelineData.connections.map(conn => {
        const fromTask = allTasks.value.find(t => t.id === conn.from)
        const toTask = allTasks.value.find(t => t.id === conn.to)
        return {
          ...conn,
          fromPos: fromTask?.position,
          toPos: toTask?.position
        }
      })
    })

    // 方法
    const goBack = () => {
      router.push('/')
    }

    const getConnectionPath = (connection) => {
      if (!connection.fromPos || !connection.toPos) return ''
      
      const { x: x1, y: y1 } = connection.fromPos
      const { x: x2, y: y2 } = connection.toPos
      
      const midX = (x1 + x2) / 2
      
      return `M ${x1 + 60} ${y1 + 20} C ${midX} ${y1 + 20} ${midX} ${y2 + 20} ${x2} ${y2 + 20}`
    }

    const showAddParallelButton = (stage) => {
      selectedStage.value = stage
      showParallelButton.value = true
      parallelButtonStyle.value = {
        left: `${stage.position.x + 30}px`,
        top: `${stage.position.y + 60}px`
      }
    }

    const hideAddParallelButton = () => {
      showParallelButton.value = false
      selectedStage.value = null
    }

    const showAddSerialButton = (event, connection) => {
      showSerialButton.value = true
      const rect = canvas.value.getBoundingClientRect()
      serialButtonStyle.value = {
        left: `${event.clientX - rect.left - 12}px`,
        top: `${event.clientY - rect.top - 12}px`
      }
    }

    const hideAddSerialButton = () => {
      showSerialButton.value = false
    }

    const addParallelTask = () => {
      showTaskSelector.value = true
      hideAddParallelButton()
    }

    const addSerialTask = () => {
      showTaskSelector.value = true
      hideAddSerialButton()
    }

    const closeTaskSelector = () => {
      showTaskSelector.value = false
    }

    const selectTask = (taskType) => {
      // 创建新任务并添加到流水线
      console.log('Selected task type:', taskType)
      closeTaskSelector()
    }

    const editTask = (task) => {
      selectedTask.value = task
      showTaskConfig.value = true
    }

    const closeTaskConfig = () => {
      showTaskConfig.value = false
      selectedTask.value = null
    }

    const saveTaskConfig = (config) => {
      console.log('Save task config:', config)
      closeTaskConfig()
    }

    onMounted(() => {
      // 初始化画布尺寸
      if (canvas.value) {
        const rect = canvas.value.getBoundingClientRect()
        canvasWidth.value = rect.width
        canvasHeight.value = rect.height
      }
    })

    return {
      canvas,
      pipelineData,
      canvasWidth,
      canvasHeight,
      showParallelButton,
      showSerialButton,
      showTaskSelector,
      showTaskConfig,
      selectedTask,
      parallelButtonStyle,
      serialButtonStyle,
      allTasks,
      connections,
      goBack,
      getConnectionPath,
      showAddParallelButton,
      hideAddParallelButton,
      showAddSerialButton,
      hideAddSerialButton,
      addParallelTask,
      addSerialTask,
      closeTaskSelector,
      selectTask,
      editTask,
      closeTaskConfig,
      saveTaskConfig
    }
  }
}
</script>

<style scoped>
.pipeline-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.editor-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 24px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn-back {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.btn-back:hover {
  background-color: #f0f0f0;
}

.breadcrumb-item {
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.editor-main {
  flex: 1;
  display: flex;
  position: relative;
}

.left-sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.sidebar-content {
  padding: 16px;
}

.project-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.project-icon {
  width: 32px;
  height: 32px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
}

.project-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.project-id {
  font-size: 12px;
  color: #666;
}

.add-stage-btn {
  width: 100%;
}

.canvas-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #fafbfc;
}

.pipeline-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.pipeline-canvas .connection-line {
  pointer-events: all;
}

.nodes-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.add-parallel {
  background-color: #52c41a;
}

.add-serial {
  background-color: #1890ff;
}
</style>
