<template>
  <div class="pipeline-editor">
    <!-- 头部导航 -->
    <header class="editor-header">
      <div class="header-content">
        <div class="breadcrumb">
          <button class="btn-back" @click="goBack">←</button>
          <span class="breadcrumb-item">Demo流水线项目</span>
        </div>
        <div class="header-center">
          <div class="tabs">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              :class="['tab-item', { active: activeTab === tab.key }]"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>
        </div>
        <div class="header-actions">
          <button class="btn btn-secondary">上一步</button>
          <button class="btn btn-secondary">运行流水线</button>
          <button class="btn btn-secondary">变更记录</button>
          <button class="btn btn-primary">保存并运行</button>
        </div>
      </div>
    </header>

    <!-- 编辑器主体 -->
    <div class="editor-main">
      <!-- 左侧工具栏 -->
      <div class="left-sidebar">
        <div class="sidebar-content">
          <div class="project-info">
            <div class="project-icon">C</div>
            <div class="project-details">
              <div class="project-name">master</div>
              <div class="project-id">60e41596a6b6e27b3d...</div>
            </div>
          </div>
          <button class="btn btn-secondary add-stage-btn" @click="addStage">+ 添加流水线阶段</button>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 流程构建 Tab 内容 -->
        <div v-if="activeTab === 'pipeline'" class="canvas-area" ref="canvas">
          <!-- 阶段区域 -->
          <div class="stages-container">
            <template v-for="(stage, stageIndex) in pipelineData.stages" :key="stage.id">
              <!-- 阶段列 -->
              <div class="stage-column">
                <!-- 阶段标题 -->
                <div class="stage-header">{{ stage.name }}</div>

                <!-- 阶段内容 -->
                <div class="stage-content">
                  <!-- 串行任务行 -->
                  <div class="serial-tasks-row">
                    <template v-for="(task, taskIndex) in getSerialTasks(stage)" :key="task.id">
                      <!-- 任务节点 -->
                      <div class="task-wrapper">
                        <TaskNode
                          :task="task"
                          @click="editTask(task)"
                        />
                      </div>

                      <!-- 串行连接线 -->
                      <div
                        v-if="taskIndex < getSerialTasks(stage).length - 1"
                        class="serial-connection"
                        @mouseenter="showAddSerialButton($event, stage, taskIndex)"
                        @mouseleave="hideAddSerialButton"
                      >
                        <div class="serial-line"></div>
                        <div class="serial-add-btn" v-if="showSerialButton">+</div>
                      </div>
                    </template>
                  </div>

                  <!-- 并行任务区域 -->
                  <div
                    v-if="getParallelTasks(stage).length > 0"
                    class="parallel-tasks-area"
                  >
                    <div
                      v-for="task in getParallelTasks(stage)"
                      :key="task.id"
                      class="parallel-task-wrapper"
                    >
                      <TaskNode
                        :task="task"
                        @click="editTask(task)"
                      />
                    </div>

                    <!-- 汇聚点 -->
                    <div class="convergence-area">
                      <div class="convergence-point"></div>
                    </div>
                  </div>

                  <!-- 添加任务按钮 -->
                  <div class="add-task-area">
                    <button
                      class="add-task-btn serial"
                      @click="showTaskSelector = true; selectedStage = stage; taskType = 'serial'"
                    >
                      + 添加串行任务
                    </button>
                    <button
                      class="add-task-btn parallel"
                      @click="showTaskSelector = true; selectedStage = stage; taskType = 'parallel'"
                    >
                      + 添加并行任务
                    </button>
                  </div>
                </div>
              </div>

              <!-- 阶段连接器 -->
              <div
                v-if="stageIndex < pipelineData.stages.length - 1"
                class="stage-connector"
              >
                <!-- 连接线 -->
                <div class="connector-lines">
                  <div class="horizontal-line"></div>
                </div>

                <!-- 添加阶段按钮 -->
                <div class="add-stage-area">
                  <button
                    class="add-stage-btn"
                    @click="addStageAfter(stageIndex)"
                  >
                    + 添加阶段
                  </button>
                </div>
              </div>
            </template>
          </div>

          <!-- SVG 连接线 -->
          <svg class="pipeline-canvas" :width="canvasWidth" :height="canvasHeight">
            <g class="connections">
              <!-- 跨阶段连接线 -->
              <path
                v-for="connection in connections"
                :key="`${connection.from}-${connection.to}`"
                :d="getConnectionPath(connection)"
                class="connection-line"
                :class="{ 'parallel-connection': connection.type === 'parallel-to-serial' }"
                @mouseenter="showAddSerialButtonOnConnection($event, connection)"
                @mouseleave="hideAddSerialButton"
              />

              <!-- 并行任务汇聚线 -->
              <g v-for="stage in pipelineData.stages" :key="`convergence-${stage.id}`">
                <path
                  v-if="stage.parallelTasks.length > 1"
                  v-for="(task, index) in stage.parallelTasks"
                  :key="`parallel-line-${task.id}`"
                  :d="getParallelConvergencePath(stage, task, index)"
                  class="parallel-convergence-line"
                />
              </g>
            </g>
          </svg>

          <!-- 添加并行任务按钮 -->
          <button
            v-if="showParallelButton"
            class="add-button add-parallel"
            :style="parallelButtonStyle"
            @click="addParallelTask"
          >
            +
          </button>

          <!-- 添加串行任务按钮 -->
          <button
            v-if="showSerialButton"
            class="add-button add-serial"
            :style="serialButtonStyle"
            @click="addSerialTask"
          >
            +
          </button>
        </div>

        <!-- 其他 Tab 内容 -->
        <div v-else class="tab-content">
          <div class="tab-placeholder">
            <h3>{{ tabs.find(t => t.key === activeTab)?.label }}</h3>
            <p>此功能正在开发中...</p>
          </div>
        </div>
      </div>

      <!-- 任务选择器侧边栏 -->
      <TaskSelector
        :visible="showTaskSelector"
        @close="closeTaskSelector"
        @select="selectTask"
      />

      <!-- 右侧任务编辑面板 -->
      <div class="right-panel" :class="{ open: showTaskConfig }" v-if="showTaskConfig">
        <div class="panel-header">
          <h3>{{ selectedTask ? `编辑 ${selectedTask.name}` : '任务配置' }}</h3>
          <button class="close-btn" @click="closeTaskConfig">×</button>
        </div>
        <div class="panel-content" v-if="selectedTask">
          <TaskConfigPanel
            :task="selectedTask"
            @save="saveTaskConfig"
            @close="closeTaskConfig"
          />
        </div>
      </div>

      <!-- 遮罩层 -->
      <div
        v-if="showTaskConfig"
        class="panel-overlay"
        @click="closeTaskConfig"
      ></div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { pipelineData } from '../data/mockData.js'
import TaskNode from './TaskNode.vue'
import TaskSelector from './TaskSelector.vue'
import TaskConfigPanel from './TaskConfigPanel.vue'

export default {
  name: 'PipelineEditor',
  components: {
    TaskNode,
    TaskSelector,
    TaskConfigPanel
  },
  setup() {
    const router = useRouter()
    const canvas = ref(null)

    // 响应式数据
    const activeTab = ref('pipeline')
    const canvasWidth = ref(1400)
    const canvasHeight = ref(600)
    const showParallelButton = ref(false)
    const showSerialButton = ref(false)
    const showTaskSelector = ref(false)
    const showTaskConfig = ref(false)
    const selectedTask = ref(null)
    const selectedStage = ref(null)
    const selectedSerialGroup = ref(null)
    const selectedTaskIndex = ref(null)
    const taskType = ref('serial')
    const parallelButtonStyle = ref({})
    const serialButtonStyle = ref({})

    // Tab 配置
    const tabs = ref([
      { key: 'basic', label: '基本信息' },
      { key: 'pipeline', label: '流程构建' },
      { key: 'trigger', label: '触发条件' },
      { key: 'variables', label: '变量和缓存' }
    ])

    // 计算属性
    const allTasks = computed(() => {
      return pipelineData.stages.flatMap(stage => stage.tasks)
    })

    // 获取串行任务
    const getSerialTasks = (stage) => {
      return stage.tasks
        .filter(task => task.isSerial)
        .sort((a, b) => a.serialOrder - b.serialOrder)
    }

    // 获取并行任务
    const getParallelTasks = (stage) => {
      return stage.tasks.filter(task => !task.isSerial)
    }

    const connections = computed(() => {
      return pipelineData.connections.map(conn => {
        const fromTask = allTasks.value.find(t => t.id === conn.from)
        const toTask = allTasks.value.find(t => t.id === conn.to)

        // 计算任务在阶段中的位置
        const fromStageIndex = pipelineData.stages.findIndex(s =>
          (s.serialGroups && s.serialGroups.some(g => g.tasks.some(t => t.id === conn.from))) ||
          (s.parallelTasks && s.parallelTasks.some(t => t.id === conn.from))
        )
        const toStageIndex = pipelineData.stages.findIndex(s =>
          (s.serialGroups && s.serialGroups.some(g => g.tasks.some(t => t.id === conn.to))) ||
          (s.parallelTasks && s.parallelTasks.some(t => t.id === conn.to))
        )

        return {
          ...conn,
          fromTask,
          toTask,
          fromStageIndex,
          toStageIndex,
          fromPos: {
            x: fromStageIndex * 360 + 150,
            y: 100
          },
          toPos: {
            x: toStageIndex * 360 + 150,
            y: 100
          }
        }
      })
    })

    // 方法
    const goBack = () => {
      router.push('/')
    }

    const getConnectionPath = (connection) => {
      if (!connection.fromPos || !connection.toPos) return ''

      const { x: x1, y: y1 } = connection.fromPos
      const { x: x2, y: y2 } = connection.toPos

      // 跨阶段连接线
      const stageWidth = 300
      const dividerWidth = 80
      const startX = x1 + stageWidth
      const endX = x2
      const midX = startX + dividerWidth / 2

      if (connection.type === 'parallel-to-serial') {
        // 并行任务汇聚到串行任务的连接
        return `M ${startX} ${y1 + 150} L ${midX} ${y1 + 150} L ${midX} ${y2 + 80} L ${endX} ${y2 + 80}`
      } else {
        // 普通串行连接
        return `M ${startX} ${y1 + 80} L ${midX} ${y1 + 80} L ${midX} ${y2 + 80} L ${endX} ${y2 + 80}`
      }
    }

    const getParallelConvergencePath = (stage, task, index) => {
      const stageIndex = pipelineData.stages.findIndex(s => s.id === stage.id)
      const stageWidth = 300
      const taskHeight = 60
      const startY = 120 + index * (taskHeight + 16)
      const convergenceY = 120 + stage.parallelTasks.length * (taskHeight + 16) + 20
      const x = stageIndex * (stageWidth + 80) + stageWidth / 2

      return `M ${x + 120} ${startY + 30} L ${x + 140} ${startY + 30} L ${x + 140} ${convergenceY} L ${x + 120} ${convergenceY}`
    }

    const getStageHoverStyle = (stage) => {
      return {
        position: 'absolute',
        left: `${stage.position.x}px`,
        top: `${stage.position.y + 40}px`,
        width: `${stage.width}px`,
        height: '300px',
        zIndex: 1
      }
    }

    const showAddParallelButton = (stage) => {
      selectedStage.value = stage
      showParallelButton.value = true
      // 计算按钮位置，放在并行任务区域底部
      const stageIndex = pipelineData.stages.findIndex(s => s.id === stage.id)
      const stageWidth = stage.width || 300
      parallelButtonStyle.value = {
        left: `${(stageIndex * (stageWidth + 60)) + stageWidth / 2 - 12}px`,
        top: `${300 + stage.parallelTasks.length * 60}px`,
        zIndex: 20
      }
    }

    const showAddSerialButton = (event, serialGroup, taskIndex) => {
      selectedSerialGroup.value = serialGroup
      selectedTaskIndex.value = taskIndex
      showSerialButton.value = true
      const rect = canvas.value.getBoundingClientRect()
      serialButtonStyle.value = {
        left: `${event.clientX - rect.left - 12}px`,
        top: `${event.clientY - rect.top - 12}px`,
        zIndex: 20
      }
    }

    const hideAddParallelButton = () => {
      showParallelButton.value = false
      selectedStage.value = null
    }

    const showAddSerialButtonOnConnection = (event, connection) => {
      showSerialButton.value = true
      const rect = canvas.value.getBoundingClientRect()
      serialButtonStyle.value = {
        left: `${event.clientX - rect.left - 12}px`,
        top: `${event.clientY - rect.top - 12}px`,
        zIndex: 20
      }
    }

    const hideAddSerialButton = () => {
      showSerialButton.value = false
    }

    const addStage = () => {
      const newStage = {
        id: `stage-${Date.now()}`,
        name: '新阶段',
        tasks: []
      }
      pipelineData.stages.push(newStage)
    }

    const addStageAfter = (index) => {
      const newStage = {
        id: `stage-${Date.now()}`,
        name: '新阶段',
        tasks: []
      }
      pipelineData.stages.splice(index + 1, 0, newStage)
    }

    const addParallelTask = () => {
      showTaskSelector.value = true
      hideAddParallelButton()
    }

    const addSerialTask = () => {
      showTaskSelector.value = true
      hideAddSerialButton()
    }

    const closeTaskSelector = () => {
      showTaskSelector.value = false
    }

    const selectTask = (taskTypeData) => {
      const newTask = {
        id: `task-${Date.now()}`,
        name: taskTypeData.name,
        type: taskTypeData.id,
        status: 'pending'
      }

      if (selectedStage.value) {
        if (taskType.value === 'serial') {
          // 添加串行任务
          const serialTasks = getSerialTasks(selectedStage.value)
          newTask.isSerial = true
          newTask.serialOrder = serialTasks.length + 1
        } else {
          // 添加并行任务
          newTask.isSerial = false
          newTask.parallelGroup = 1
        }
        selectedStage.value.tasks.push(newTask)
      }

      closeTaskSelector()
    }

    const editTask = (task) => {
      selectedTask.value = task
      showTaskConfig.value = true
    }

    const closeTaskConfig = () => {
      showTaskConfig.value = false
      selectedTask.value = null
    }

    const saveTaskConfig = (config) => {
      if (selectedTask.value) {
        Object.assign(selectedTask.value, config)
      }
      closeTaskConfig()
    }

    onMounted(() => {
      // 初始化画布尺寸
      if (canvas.value) {
        const rect = canvas.value.getBoundingClientRect()
        canvasWidth.value = rect.width
        canvasHeight.value = rect.height
      }
    })

    return {
      canvas,
      pipelineData,
      activeTab,
      tabs,
      canvasWidth,
      canvasHeight,
      showParallelButton,
      showSerialButton,
      showTaskSelector,
      showTaskConfig,
      selectedTask,
      selectedStage,
      taskType,
      parallelButtonStyle,
      serialButtonStyle,
      allTasks,
      connections,
      goBack,
      getConnectionPath,
      getParallelConvergencePath,
      getStageHoverStyle,
      getSerialTasks,
      getParallelTasks,
      showAddParallelButton,
      hideAddParallelButton,
      showAddSerialButton,
      showAddSerialButtonOnConnection,
      hideAddSerialButton,
      addStage,
      addStageAfter,
      addParallelTask,
      addSerialTask,
      closeTaskSelector,
      selectTask,
      editTask,
      closeTaskConfig,
      saveTaskConfig
    }
  }
}
</script>

<style scoped>
.pipeline-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.editor-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 24px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.header-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn-back {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.btn-back:hover {
  background-color: #f0f0f0;
}

.breadcrumb-item {
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Tab 样式 */
.tabs {
  display: flex;
  gap: 0;
}

.tab-item {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-item:hover {
  color: #1890ff;
}

.tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.editor-main {
  flex: 1;
  display: flex;
  position: relative;
}

.left-sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.sidebar-content {
  padding: 16px;
}

.project-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.project-icon {
  width: 32px;
  height: 32px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
}

.project-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.project-id {
  font-size: 12px;
  color: #666;
}

.add-stage-btn {
  width: 100%;
}

.main-content {
  flex: 1;
  position: relative;
}

.canvas-area {
  width: 100%;
  height: calc(100vh - 120px);
  position: relative;
  overflow: auto;
  background: #fafbfc;
  padding: 40px 20px;
}

/* 阶段容器 */
.stages-container {
  display: flex;
  align-items: flex-start;
  gap: 0;
  min-height: 100%;
  position: relative;
}

.stage-column {
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  padding: 0;
  min-width: 280px;
  max-width: 280px;
  position: relative;
}

.stage-header {
  background: #f8f9fa;
  padding: 16px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
}

.stage-content {
  padding: 20px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 串行任务行 */
.serial-tasks-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
  margin-bottom: 20px;
}

.task-wrapper {
  flex-shrink: 0;
}

.serial-connection {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 40px;
  flex-shrink: 0;
}

.serial-line {
  width: 40px;
  height: 2px;
  background: #1890ff;
  position: relative;
}

.serial-add-btn {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  z-index: 10;
}

.serial-connection:hover .serial-add-btn {
  display: flex;
}

.serial-connection:hover .serial-line {
  background: #40a9ff;
  height: 3px;
}

/* 并行任务区域 */
.parallel-tasks-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(82, 196, 26, 0.05);
  border-radius: 8px;
  border: 1px dashed #52c41a;
  position: relative;
  margin-bottom: 20px;
}

.parallel-task-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
}

/* 汇聚区域 */
.convergence-area {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.convergence-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #52c41a;
  position: relative;
}

.convergence-point::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 20px;
  background: #52c41a;
}

/* 添加任务区域 */
.add-task-area {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.add-task-btn {
  padding: 8px 16px;
  border: 1px dashed #d9d9d9;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.2s;
}

.add-task-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.add-task-btn.serial {
  border-color: #1890ff;
  color: #1890ff;
}

.add-task-btn.parallel {
  border-color: #52c41a;
  color: #52c41a;
}

/* 阶段连接器 */
.stage-connector {
  position: relative;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: #fafbfc;
  min-height: 400px;
}

.connector-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.horizontal-line {
  width: 80px;
  height: 2px;
  background: #1890ff;
  position: relative;
}

.horizontal-line::before,
.horizontal-line::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #1890ff;
  top: 50%;
  transform: translateY(-50%);
}

.horizontal-line::before {
  left: -4px;
}

.horizontal-line::after {
  right: -4px;
}

.add-stage-area {
  position: relative;
  z-index: 10;
}

.add-stage-btn {
  background: white;
  border: 1px dashed #1890ff;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 12px;
  color: #1890ff;
  transition: all 0.2s;
  white-space: nowrap;
}

.add-stage-btn:hover {
  background: #f0f8ff;
  border-style: solid;
}

.pipeline-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 5;
}

.pipeline-canvas .connection-line {
  pointer-events: all;
  stroke: #1890ff;
  stroke-width: 2;
  fill: none;
}

.pipeline-canvas .connection-line:hover {
  stroke: #40a9ff;
  stroke-width: 3;
}

.pipeline-canvas .parallel-connection {
  stroke: #52c41a;
  stroke-dasharray: 5,5;
}

.pipeline-canvas .parallel-convergence-line {
  stroke: #52c41a;
  stroke-width: 1;
  fill: none;
}

.add-button {
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.add-parallel {
  background-color: #52c41a;
}

.add-parallel:hover {
  background-color: #73d13d;
  transform: scale(1.1);
}

.add-serial {
  background-color: #1890ff;
}

.add-serial:hover {
  background-color: #40a9ff;
  transform: scale(1.1);
}

/* 右侧面板样式 */
.right-panel {
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.right-panel.open {
  transform: translateX(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  color: #666;
}

.close-btn:hover {
  background-color: #f0f0f0;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
}

/* Tab 内容样式 */
.tab-content {
  padding: 40px;
  text-align: center;
}

.tab-placeholder h3 {
  color: #666;
  margin-bottom: 16px;
}

.tab-placeholder p {
  color: #999;
}
</style>
