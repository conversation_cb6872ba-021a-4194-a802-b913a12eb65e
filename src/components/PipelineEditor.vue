<template>
  <div class="pipeline-editor">
    <!-- 头部导航 -->
    <header class="editor-header">
      <div class="header-content">
        <div class="breadcrumb">
          <button class="btn-back" @click="goBack">←</button>
          <span class="breadcrumb-item">Demo流水线项目</span>
        </div>
        <div class="header-center">
          <div class="tabs">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              :class="['tab-item', { active: activeTab === tab.key }]"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>
        </div>
        <div class="header-actions">
          <button class="btn btn-secondary">上一步</button>
          <button class="btn btn-secondary">运行流水线</button>
          <button class="btn btn-secondary">变更记录</button>
          <button class="btn btn-primary">保存并运行</button>
        </div>
      </div>
    </header>

    <!-- 编辑器主体 -->
    <div class="editor-main">
      <!-- 左侧工具栏 -->
      <div class="left-sidebar">
        <div class="sidebar-content">
          <div class="project-info">
            <div class="project-icon">C</div>
            <div class="project-details">
              <div class="project-name">master</div>
              <div class="project-id">60e41596a6b6e27b3d...</div>
            </div>
          </div>
          <button class="btn btn-secondary add-stage-btn" @click="addStage">+ 添加流水线阶段</button>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 流程构建 Tab 内容 -->
        <div v-if="activeTab === 'pipeline'" class="canvas-area" ref="canvas">
          <!-- 阶段区域 - 垂直平铺 -->
          <div class="stage-columns">
            <template v-for="(stage, stageIndex) in pipelineData.stages" :key="stage.id">
              <!-- 阶段列 -->
              <div class="stage-column">
                <!-- 阶段标题 -->
                <div class="stage-title">{{ stage.name }}</div>

                <!-- 阶段内容区域 -->
                <div class="stage-content">
                  <!-- 串行任务组 - 水平布局 -->
                  <div
                    v-for="serialGroup in stage.serialGroups"
                    :key="serialGroup.id"
                    class="serial-group"
                  >
                    <div class="serial-tasks">
                      <TaskNode
                        v-for="(task, taskIndex) in serialGroup.tasks"
                        :key="task.id"
                        :task="task"
                        @click="editTask(task)"
                      />
                      <!-- 串行任务之间的连接线 -->
                      <div
                        v-for="(task, taskIndex) in serialGroup.tasks.slice(0, -1)"
                        :key="`serial-line-${task.id}`"
                        class="serial-connection"
                        @mouseenter="showAddSerialButton($event, serialGroup, taskIndex)"
                        @mouseleave="hideAddSerialButton"
                      >
                        <div class="serial-line"></div>
                      </div>
                    </div>
                  </div>

                  <!-- 并行任务 - 垂直布局 -->
                  <div
                    v-if="stage.parallelTasks.length > 0"
                    class="parallel-group"
                    @mouseenter="showAddParallelButton(stage)"
                    @mouseleave="hideAddParallelButton"
                  >
                    <TaskNode
                      v-for="task in stage.parallelTasks"
                      :key="task.id"
                      :task="task"
                      @click="editTask(task)"
                    />
                  </div>
                </div>
              </div>

              <!-- 阶段分隔线和添加按钮 -->
              <div
                v-if="stageIndex < pipelineData.stages.length - 1"
                class="stage-divider"
              >
                <div class="divider-line"></div>
                <button class="add-stage-button" @click="addStageAfter(stageIndex)">+</button>
              </div>
            </template>

            <!-- 最后一个阶段后的添加按钮 -->
            <div class="stage-divider">
              <div class="divider-line"></div>
              <button class="add-stage-button" @click="addStage">+</button>
            </div>
          </div>

          <!-- SVG 连接线 -->
          <svg class="pipeline-canvas" :width="canvasWidth" :height="canvasHeight">
            <g class="connections">
              <path
                v-for="connection in connections"
                :key="`${connection.from}-${connection.to}`"
                :d="getConnectionPath(connection)"
                class="connection-line"
                @mouseenter="showAddSerialButtonOnConnection($event, connection)"
                @mouseleave="hideAddSerialButton"
              />
            </g>
          </svg>

          <!-- 添加并行任务按钮 -->
          <button
            v-if="showParallelButton"
            class="add-button add-parallel"
            :style="parallelButtonStyle"
            @click="addParallelTask"
          >
            +
          </button>

          <!-- 添加串行任务按钮 -->
          <button
            v-if="showSerialButton"
            class="add-button add-serial"
            :style="serialButtonStyle"
            @click="addSerialTask"
          >
            +
          </button>
        </div>

        <!-- 其他 Tab 内容 -->
        <div v-else class="tab-content">
          <div class="tab-placeholder">
            <h3>{{ tabs.find(t => t.key === activeTab)?.label }}</h3>
            <p>此功能正在开发中...</p>
          </div>
        </div>
      </div>

      <!-- 任务选择器侧边栏 -->
      <TaskSelector
        :visible="showTaskSelector"
        @close="closeTaskSelector"
        @select="selectTask"
      />

      <!-- 右侧任务编辑面板 -->
      <div class="right-panel" :class="{ open: showTaskConfig }" v-if="showTaskConfig">
        <div class="panel-header">
          <h3>{{ selectedTask ? `编辑 ${selectedTask.name}` : '任务配置' }}</h3>
          <button class="close-btn" @click="closeTaskConfig">×</button>
        </div>
        <div class="panel-content" v-if="selectedTask">
          <TaskConfigPanel
            :task="selectedTask"
            @save="saveTaskConfig"
            @close="closeTaskConfig"
          />
        </div>
      </div>

      <!-- 遮罩层 -->
      <div
        v-if="showTaskConfig"
        class="panel-overlay"
        @click="closeTaskConfig"
      ></div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { pipelineData } from '../data/mockData.js'
import TaskNode from './TaskNode.vue'
import TaskSelector from './TaskSelector.vue'
import TaskConfigPanel from './TaskConfigPanel.vue'

export default {
  name: 'PipelineEditor',
  components: {
    TaskNode,
    TaskSelector,
    TaskConfigPanel
  },
  setup() {
    const router = useRouter()
    const canvas = ref(null)

    // 响应式数据
    const activeTab = ref('pipeline')
    const canvasWidth = ref(1400)
    const canvasHeight = ref(600)
    const showParallelButton = ref(false)
    const showSerialButton = ref(false)
    const showTaskSelector = ref(false)
    const showTaskConfig = ref(false)
    const selectedTask = ref(null)
    const selectedStage = ref(null)
    const selectedSerialGroup = ref(null)
    const selectedTaskIndex = ref(null)
    const parallelButtonStyle = ref({})
    const serialButtonStyle = ref({})

    // Tab 配置
    const tabs = ref([
      { key: 'basic', label: '基本信息' },
      { key: 'pipeline', label: '流程构建' },
      { key: 'trigger', label: '触发条件' },
      { key: 'variables', label: '变量和缓存' }
    ])

    // 计算属性
    const allTasks = computed(() => {
      return pipelineData.stages.flatMap(stage => [
        ...stage.serialGroups.flatMap(group => group.tasks),
        ...stage.parallelTasks
      ])
    })

    const connections = computed(() => {
      return pipelineData.connections.map(conn => {
        const fromTask = allTasks.value.find(t => t.id === conn.from)
        const toTask = allTasks.value.find(t => t.id === conn.to)

        // 计算任务在阶段中的位置
        const fromStageIndex = pipelineData.stages.findIndex(s => s.tasks.some(t => t.id === conn.from))
        const toStageIndex = pipelineData.stages.findIndex(s => s.tasks.some(t => t.id === conn.to))

        const fromTaskIndex = pipelineData.stages[fromStageIndex]?.tasks.findIndex(t => t.id === conn.from) || 0
        const toTaskIndex = pipelineData.stages[toStageIndex]?.tasks.findIndex(t => t.id === conn.to) || 0

        return {
          ...conn,
          fromPos: {
            x: fromStageIndex * 360 + 150,
            y: 100 + fromTaskIndex * 70
          },
          toPos: {
            x: toStageIndex * 360 + 150,
            y: 100 + toTaskIndex * 70
          }
        }
      })
    })

    // 方法
    const goBack = () => {
      router.push('/')
    }

    const getConnectionPath = (connection) => {
      if (!connection.fromPos || !connection.toPos) return ''

      const { x: x1, y: y1 } = connection.fromPos
      const { x: x2, y: y2 } = connection.toPos

      // 使用直角连接线，更符合流水线风格
      if (connection.type === 'parallel-to-serial') {
        // 并行任务汇聚到串行任务的连接
        const midX = x1 + 70
        const midY = y2 + 20
        return `M ${x1 + 60} ${y1 + 20} L ${midX} ${y1 + 20} L ${midX} ${midY} L ${x2} ${midY}`
      } else {
        // 普通串行连接
        const midX = (x1 + x2) / 2
        return `M ${x1 + 60} ${y1 + 20} L ${midX} ${y1 + 20} L ${midX} ${y2 + 20} L ${x2} ${y2 + 20}`
      }
    }

    const getStageHoverStyle = (stage) => {
      return {
        position: 'absolute',
        left: `${stage.position.x}px`,
        top: `${stage.position.y + 40}px`,
        width: `${stage.width}px`,
        height: '300px',
        zIndex: 1
      }
    }

    const showAddParallelButton = (stage) => {
      selectedStage.value = stage
      showParallelButton.value = true
      // 计算按钮位置，放在并行任务区域底部
      const stageIndex = pipelineData.stages.findIndex(s => s.id === stage.id)
      const stageWidth = stage.width || 300
      parallelButtonStyle.value = {
        left: `${(stageIndex * (stageWidth + 60)) + stageWidth / 2 - 12}px`,
        top: `${300 + stage.parallelTasks.length * 60}px`,
        zIndex: 20
      }
    }

    const showAddSerialButton = (event, serialGroup, taskIndex) => {
      selectedSerialGroup.value = serialGroup
      selectedTaskIndex.value = taskIndex
      showSerialButton.value = true
      const rect = canvas.value.getBoundingClientRect()
      serialButtonStyle.value = {
        left: `${event.clientX - rect.left - 12}px`,
        top: `${event.clientY - rect.top - 12}px`,
        zIndex: 20
      }
    }

    const hideAddParallelButton = () => {
      showParallelButton.value = false
      selectedStage.value = null
    }

    const showAddSerialButtonOnConnection = (event, connection) => {
      showSerialButton.value = true
      const rect = canvas.value.getBoundingClientRect()
      serialButtonStyle.value = {
        left: `${event.clientX - rect.left - 12}px`,
        top: `${event.clientY - rect.top - 12}px`,
        zIndex: 20
      }
    }

    const hideAddSerialButton = () => {
      showSerialButton.value = false
    }

    const addStage = () => {
      const newStage = {
        id: `stage-${Date.now()}`,
        name: '新阶段',
        width: 300,
        serialGroups: [],
        parallelTasks: []
      }
      pipelineData.stages.push(newStage)
    }

    const addStageAfter = (index) => {
      const newStage = {
        id: `stage-${Date.now()}`,
        name: '新阶段',
        width: 300,
        serialGroups: [],
        parallelTasks: []
      }
      pipelineData.stages.splice(index + 1, 0, newStage)
    }

    const addParallelTask = () => {
      showTaskSelector.value = true
      hideAddParallelButton()
    }

    const addSerialTask = () => {
      showTaskSelector.value = true
      hideAddSerialButton()
    }

    const closeTaskSelector = () => {
      showTaskSelector.value = false
    }

    const selectTask = (taskType) => {
      const newTask = {
        id: `task-${Date.now()}`,
        name: taskType.name,
        type: taskType.id,
        status: 'pending'
      }

      if (selectedStage.value) {
        // 添加并行任务
        selectedStage.value.parallelTasks.push(newTask)
      } else if (selectedSerialGroup.value && selectedTaskIndex.value !== null) {
        // 在串行任务中间插入新任务
        selectedSerialGroup.value.tasks.splice(selectedTaskIndex.value + 1, 0, newTask)
      }

      closeTaskSelector()
    }

    const editTask = (task) => {
      selectedTask.value = task
      showTaskConfig.value = true
    }

    const closeTaskConfig = () => {
      showTaskConfig.value = false
      selectedTask.value = null
    }

    const saveTaskConfig = (config) => {
      if (selectedTask.value) {
        Object.assign(selectedTask.value, config)
      }
      closeTaskConfig()
    }

    onMounted(() => {
      // 初始化画布尺寸
      if (canvas.value) {
        const rect = canvas.value.getBoundingClientRect()
        canvasWidth.value = rect.width
        canvasHeight.value = rect.height
      }
    })

    return {
      canvas,
      pipelineData,
      activeTab,
      tabs,
      canvasWidth,
      canvasHeight,
      showParallelButton,
      showSerialButton,
      showTaskSelector,
      showTaskConfig,
      selectedTask,
      parallelButtonStyle,
      serialButtonStyle,
      allTasks,
      connections,
      goBack,
      getConnectionPath,
      getStageHoverStyle,
      showAddParallelButton,
      hideAddParallelButton,
      showAddSerialButton,
      showAddSerialButtonOnConnection,
      hideAddSerialButton,
      addStage,
      addStageAfter,
      addParallelTask,
      addSerialTask,
      closeTaskSelector,
      selectTask,
      editTask,
      closeTaskConfig,
      saveTaskConfig
    }
  }
}
</script>

<style scoped>
.pipeline-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.editor-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 24px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.header-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn-back {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.btn-back:hover {
  background-color: #f0f0f0;
}

.breadcrumb-item {
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Tab 样式 */
.tabs {
  display: flex;
  gap: 0;
}

.tab-item {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-item:hover {
  color: #1890ff;
}

.tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.editor-main {
  flex: 1;
  display: flex;
  position: relative;
}

.left-sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.sidebar-content {
  padding: 16px;
}

.project-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.project-icon {
  width: 32px;
  height: 32px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
}

.project-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.project-id {
  font-size: 12px;
  color: #666;
}

.add-stage-btn {
  width: 100%;
}

.main-content {
  flex: 1;
  position: relative;
}

.canvas-area {
  width: 100%;
  height: calc(100vh - 120px);
  position: relative;
  overflow: auto;
  background: #fafbfc;
  padding: 20px;
}

/* 阶段列布局 */
.stage-columns {
  display: flex;
  gap: 0;
  min-height: 100%;
  position: relative;
}

.stage-column {
  background: #f8f9fa;
  padding: 16px;
  min-height: 100%;
  position: relative;
  flex: 1;
  border-right: 1px solid #e8e8e8;
}

.stage-column:last-child {
  border-right: none;
}

.stage-title {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #1890ff;
}

.stage-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: calc(100% - 60px);
  position: relative;
}

/* 串行任务组 */
.serial-group {
  position: relative;
}

.serial-tasks {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.serial-connection {
  position: relative;
  cursor: pointer;
}

.serial-line {
  width: 20px;
  height: 2px;
  background: #1890ff;
  position: relative;
}

.serial-connection:hover .serial-line {
  background: #40a9ff;
  height: 3px;
}

/* 并行任务组 */
.parallel-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 8px;
  border: 1px dashed #1890ff;
}

/* 阶段分隔线 */
.stage-divider {
  position: relative;
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  background: #fafbfc;
}

.divider-line {
  width: 2px;
  height: 100%;
  background: #d9d9d9;
  position: relative;
}

.add-stage-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  z-index: 15;
}

.add-stage-button:hover {
  background: #40a9ff;
  transform: translate(-50%, -50%) scale(1.1);
}

.pipeline-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 5;
}

.pipeline-canvas .connection-line {
  pointer-events: all;
  stroke: #1890ff;
  stroke-width: 2;
  fill: none;
}

.pipeline-canvas .connection-line:hover {
  stroke: #40a9ff;
  stroke-width: 3;
}

.add-button {
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.add-parallel {
  background-color: #52c41a;
}

.add-parallel:hover {
  background-color: #73d13d;
  transform: scale(1.1);
}

.add-serial {
  background-color: #1890ff;
}

.add-serial:hover {
  background-color: #40a9ff;
  transform: scale(1.1);
}

/* 右侧面板样式 */
.right-panel {
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.right-panel.open {
  transform: translateX(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  color: #666;
}

.close-btn:hover {
  background-color: #f0f0f0;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
}

/* Tab 内容样式 */
.tab-content {
  padding: 40px;
  text-align: center;
}

.tab-placeholder h3 {
  color: #666;
  margin-bottom: 16px;
}

.tab-placeholder p {
  color: #999;
}
</style>
