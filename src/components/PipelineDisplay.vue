<template>
  <div class="pipeline-display">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-content">
        <div class="breadcrumb">
          <span class="breadcrumb-item">Demo流水线项目</span>
        </div>
        <div class="header-actions">
          <button class="btn btn-secondary">配置变更</button>
          <button class="btn btn-secondary">运行历史</button>
          <button class="btn btn-secondary">变更记录</button>
          <button class="btn btn-primary" @click="goToEditor">编辑</button>
        </div>
      </div>
    </header>

    <!-- 流水线信息卡片 -->
    <div class="pipeline-info">
      <div class="info-cards">
        <!-- Java代码扫描卡片 -->
        <div class="info-card">
          <div class="card-header">
            <span class="status-indicator status-success"></span>
            <h3>Java代码扫描</h3>
          </div>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-number red">6</span>
              <span class="stat-label">严重</span>
            </div>
            <div class="stat-item">
              <span class="stat-number orange">2</span>
              <span class="stat-label">主要</span>
            </div>
            <div class="stat-item">
              <span class="stat-number yellow">2</span>
              <span class="stat-label">次要</span>
            </div>
            <div class="stat-item">
              <span class="stat-number gray">2</span>
              <span class="stat-label">提示</span>
            </div>
          </div>
          <div class="card-footer">
            <span>Java代码扫描结果</span>
            <span>1分2秒</span>
          </div>
        </div>

        <!-- Java安全扫描卡片 -->
        <div class="info-card">
          <div class="card-header">
            <span class="status-indicator status-success"></span>
            <h3>Java安全扫描</h3>
          </div>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-number red">6</span>
              <span class="stat-label">严重</span>
            </div>
            <div class="stat-item">
              <span class="stat-number orange">0</span>
              <span class="stat-label">主要</span>
            </div>
            <div class="stat-item">
              <span class="stat-number yellow">0</span>
              <span class="stat-label">次要</span>
            </div>
            <div class="stat-item">
              <span class="stat-number gray">0</span>
              <span class="stat-label">提示</span>
            </div>
          </div>
          <div class="card-footer">
            <span>Java安全扫描</span>
            <span>1分</span>
          </div>
        </div>

        <!-- Java构建上传卡片 -->
        <div class="info-card">
          <div class="card-header">
            <span class="status-indicator status-success"></span>
            <h3>Java构建上传</h3>
          </div>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-number green">1</span>
              <span class="stat-label">成功构建</span>
            </div>
            <div class="stat-item">
              <span class="stat-number gray">0</span>
              <span class="stat-label">失败</span>
            </div>
            <div class="stat-item">
              <span class="stat-number gray">0</span>
              <span class="stat-label">跳过</span>
            </div>
          </div>
          <div class="card-footer">
            <span>构建</span>
            <span>1分</span>
          </div>
        </div>

        <!-- Maven部署卡片 -->
        <div class="info-card">
          <div class="card-header">
            <span class="status-indicator status-running"></span>
            <h3>Maven部署</h3>
          </div>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-number green">100.00</span>
              <span class="stat-label">覆盖率</span>
            </div>
          </div>
          <div class="card-footer">
            <span>Maven部署</span>
            <span>运行中</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 流水线执行信息 -->
    <div class="execution-info">
      <div class="execution-header">
        <h3>流水线执行</h3>
        <div class="execution-meta">
          <span>最近运行: {{ pipelineData.lastRun }}</span>
          <span>持续时间: {{ pipelineData.duration }}</span>
          <span>状态: 
            <span class="status-indicator status-success"></span>
            成功
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { pipelineData } from '../data/mockData.js'

export default {
  name: 'PipelineDisplay',
  data() {
    return {
      pipelineData
    }
  },
  methods: {
    goToEditor() {
      this.$router.push('/editor')
    }
  }
}
</script>

<style scoped>
.pipeline-display {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumb-item {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.pipeline-info {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h3 {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 500;
}

.card-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-number.red { color: #ff4d4f; }
.stat-number.orange { color: #fa8c16; }
.stat-number.yellow { color: #faad14; }
.stat-number.green { color: #52c41a; }
.stat-number.gray { color: #8c8c8c; }

.stat-label {
  font-size: 12px;
  color: #666;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.execution-info {
  padding: 0 24px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.execution-header {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.execution-header h3 {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.execution-meta {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #666;
  align-items: center;
}
</style>
