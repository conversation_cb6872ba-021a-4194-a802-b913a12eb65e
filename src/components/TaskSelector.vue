<template>
  <div class="sidebar" :class="{ open: visible }">
    <div class="sidebar-header">
      <h3>添加步骤</h3>
      <button class="close-btn" @click="$emit('close')">×</button>
    </div>
    
    <div class="sidebar-content">
      <div class="search-box">
        <input
          type="text"
          placeholder="搜索步骤类型"
          v-model="searchQuery"
          class="search-input"
        />
      </div>

      <div class="task-categories">
        <div
          v-for="category in filteredCategories"
          :key="category.category"
          class="category-section"
        >
          <h4 class="category-title">{{ category.category }}</h4>
          <div class="task-list">
            <div
              v-for="task in category.tasks"
              :key="task.id"
              class="task-item"
              @click="selectTask(task)"
            >
              <div class="task-icon">{{ task.icon }}</div>
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-description">{{ getTaskDescription(task.id) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { taskTypes } from '../data/mockData.js'

export default {
  name: 'TaskSelector',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'select'],
  setup(props, { emit }) {
    const searchQuery = ref('')

    const filteredCategories = computed(() => {
      if (!searchQuery.value) {
        return taskTypes
      }
      
      return taskTypes.map(category => ({
        ...category,
        tasks: category.tasks.filter(task =>
          task.name.toLowerCase().includes(searchQuery.value.toLowerCase())
        )
      })).filter(category => category.tasks.length > 0)
    })

    const selectTask = (task) => {
      emit('select', task)
    }

    const getTaskDescription = (taskId) => {
      const descriptions = {
        'java-code-scan': '扫描Java代码质量问题',
        'java-security-scan': '检测Java代码安全漏洞',
        'python-code-scan': '扫描Python代码质量',
        'javascript-scan': 'JavaScript代码检查',
        'maven-build': '使用Maven构建项目',
        'gradle-build': '使用Gradle构建项目',
        'npm-build': '使用NPM构建前端项目',
        'docker-build': '构建Docker镜像',
        'unit-test': '执行单元测试',
        'integration-test': '执行集成测试',
        'e2e-test': '执行端到端测试',
        'k8s-deploy': '部署到Kubernetes集群',
        'docker-deploy': '部署Docker容器',
        'ftp-deploy': '通过FTP部署文件'
      }
      return descriptions[taskId] || '执行相关任务'
    }

    return {
      searchQuery,
      filteredCategories,
      selectTask,
      getTaskDescription
    }
  }
}
</script>

<style scoped>
.sidebar {
  position: fixed;
  right: 0;
  top: 0;
  width: 350px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  color: #666;
}

.close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.search-box {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.category-section {
  margin-bottom: 24px;
}

.category-title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.task-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.task-icon {
  font-size: 20px;
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.task-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
  }
}
</style>
