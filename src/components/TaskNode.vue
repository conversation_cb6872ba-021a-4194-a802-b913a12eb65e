<template>
  <div
    class="task-node"
    :class="[`status-${task.status}`, `type-${task.type}`]"
    :style="taskStyle"
    @click="$emit('click', task)"
  >
    <div class="task-content">
      <div class="status-indicator" :class="`status-${task.status}`"></div>
      <span class="task-name">{{ task.name }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskNode',
  props: {
    task: {
      type: Object,
      required: true
    }
  },
  emits: ['click'],
  computed: {
    taskStyle() {
      return {
        // 移除绝对定位，使用flex布局
      }
    }
  }
}
</script>

<style scoped>
.task-node {
  background: white;
  border: 2px solid #d9d9d9;
  border-radius: 20px;
  padding: 10px 18px;
  min-width: 140px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.task-node:hover {
  border-color: #1890ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.task-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

/* 状态样式 */
.task-node.status-success {
  border-color: #52c41a;
  background: #f6ffed;
}

.task-node.status-running {
  border-color: #1890ff;
  background: #e6f7ff;
  animation: pulse-border 2s infinite;
}

.task-node.status-failed {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.task-node.status-pending {
  border-color: #faad14;
  background: #fffbe6;
}

@keyframes pulse-border {
  0% { border-color: #1890ff; }
  50% { border-color: #40a9ff; }
  100% { border-color: #1890ff; }
}

/* 任务类型样式 */
.task-node.type-java-code-scan .task-name::before,
.task-node.type-code-scan .task-name::before {
  content: '🔍 ';
}

.task-node.type-java-security-scan .task-name::before,
.task-node.type-security-scan .task-name::before {
  content: '🔒 ';
}

.task-node.type-maven-build .task-name::before,
.task-node.type-build .task-name::before {
  content: '🔨 ';
}

.task-node.type-k8s-deploy .task-name::before,
.task-node.type-deploy .task-name::before {
  content: '🚀 ';
}

.task-node.type-unit-test .task-name::before,
.task-node.type-test .task-name::before {
  content: '🧪 ';
}

.task-node.type-docker-build .task-name::before {
  content: '🐳 ';
}

.task-node.type-npm-build .task-name::before {
  content: '📦 ';
}
</style>
