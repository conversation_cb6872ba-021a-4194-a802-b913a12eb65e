<template>
  <div v-if="visible" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>{{ task ? `编辑 ${task.name}` : '配置任务' }}</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>

      <div class="modal-body" v-if="task">
        <div class="config-section">
          <h4>基本配置</h4>
          <div class="form-group">
            <label>任务名称</label>
            <input
              type="text"
              v-model="config.name"
              class="form-input"
              placeholder="输入任务名称"
            />
          </div>
          
          <div class="form-group">
            <label>任务描述</label>
            <textarea
              v-model="config.description"
              class="form-textarea"
              placeholder="输入任务描述"
              rows="3"
            ></textarea>
          </div>

          <div class="form-group">
            <label>超时时间 (分钟)</label>
            <input
              type="number"
              v-model="config.timeout"
              class="form-input"
              placeholder="30"
              min="1"
              max="1440"
            />
          </div>
        </div>

        <div class="config-section" v-if="task.type === 'code-scan'">
          <h4>代码扫描配置</h4>
          <div class="form-group">
            <label>扫描规则</label>
            <select v-model="config.scanRules" class="form-select">
              <option value="default">默认规则</option>
              <option value="strict">严格规则</option>
              <option value="custom">自定义规则</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="config.failOnError" />
              发现问题时失败
            </label>
          </div>
        </div>

        <div class="config-section" v-if="task.type === 'build'">
          <h4>构建配置</h4>
          <div class="form-group">
            <label>构建命令</label>
            <input
              type="text"
              v-model="config.buildCommand"
              class="form-input"
              placeholder="mvn clean package"
            />
          </div>
          
          <div class="form-group">
            <label>构建目录</label>
            <input
              type="text"
              v-model="config.buildDir"
              class="form-input"
              placeholder="./target"
            />
          </div>
        </div>

        <div class="config-section" v-if="task.type === 'deploy'">
          <h4>部署配置</h4>
          <div class="form-group">
            <label>部署环境</label>
            <select v-model="config.environment" class="form-select">
              <option value="dev">开发环境</option>
              <option value="test">测试环境</option>
              <option value="staging">预发布环境</option>
              <option value="prod">生产环境</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>部署策略</label>
            <select v-model="config.strategy" class="form-select">
              <option value="rolling">滚动更新</option>
              <option value="blue-green">蓝绿部署</option>
              <option value="canary">金丝雀部署</option>
            </select>
          </div>
        </div>

        <div class="config-section">
          <h4>高级配置</h4>
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="config.allowFailure" />
              允许失败继续
            </label>
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="config.runInParallel" />
              并行执行
            </label>
          </div>

          <div class="form-group">
            <label>环境变量</label>
            <div class="env-vars">
              <div
                v-for="(env, index) in config.envVars"
                :key="index"
                class="env-var-row"
              >
                <input
                  type="text"
                  v-model="env.key"
                  placeholder="变量名"
                  class="form-input env-key"
                />
                <input
                  type="text"
                  v-model="env.value"
                  placeholder="变量值"
                  class="form-input env-value"
                />
                <button
                  type="button"
                  @click="removeEnvVar(index)"
                  class="btn-remove"
                >
                  ×
                </button>
              </div>
              <button
                type="button"
                @click="addEnvVar"
                class="btn btn-secondary btn-small"
              >
                + 添加环境变量
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="$emit('close')">取消</button>
        <button class="btn btn-primary" @click="saveConfig">保存</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'TaskConfigModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    task: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'save'],
  setup(props, { emit }) {
    const config = ref({
      name: '',
      description: '',
      timeout: 30,
      scanRules: 'default',
      failOnError: true,
      buildCommand: '',
      buildDir: '',
      environment: 'dev',
      strategy: 'rolling',
      allowFailure: false,
      runInParallel: false,
      envVars: []
    })

    watch(() => props.task, (newTask) => {
      if (newTask) {
        config.value = {
          name: newTask.name || '',
          description: newTask.description || '',
          timeout: newTask.timeout || 30,
          scanRules: newTask.scanRules || 'default',
          failOnError: newTask.failOnError !== false,
          buildCommand: newTask.buildCommand || '',
          buildDir: newTask.buildDir || '',
          environment: newTask.environment || 'dev',
          strategy: newTask.strategy || 'rolling',
          allowFailure: newTask.allowFailure || false,
          runInParallel: newTask.runInParallel || false,
          envVars: newTask.envVars || []
        }
      }
    }, { immediate: true })

    const closeModal = () => {
      emit('close')
    }

    const saveConfig = () => {
      emit('save', { ...config.value })
    }

    const addEnvVar = () => {
      config.value.envVars.push({ key: '', value: '' })
    }

    const removeEnvVar = (index) => {
      config.value.envVars.splice(index, 1)
    }

    return {
      config,
      closeModal,
      saveConfig,
      addEnvVar,
      removeEnvVar
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e8e8e8;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  color: #666;
}

.close-btn:hover {
  background-color: #f0f0f0;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.env-vars {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.env-var-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.env-key {
  flex: 1;
}

.env-value {
  flex: 2;
}

.btn-remove {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
}

input[type="checkbox"] {
  margin-right: 8px;
}
</style>
