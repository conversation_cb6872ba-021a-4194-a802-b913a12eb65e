import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import PipelineDisplay from './components/PipelineDisplay.vue'
import PipelineEditor from './components/PipelineEditor.vue'
import './styles/main.css'

const routes = [
  { path: '/', component: PipelineDisplay },
  { path: '/editor', component: PipelineEditor }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
app.use(router)
app.mount('#app')
