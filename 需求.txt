
你的身份是一个CICD技术专家，我现在想开发一个关于CICD的一个流水线页面，下面是各功能的展示，也可根据你的经验，帮我补充对应的功能
1. 根据 "展示页.png" 生成页面，当点击 "编辑" 时，则会跳转到 编辑页面区
2. 编辑页里展示的是正在编辑的状态，效果如图"编辑页.png"所示，用户可在此页面完成对CICD各个阶段的变更操作。
流水线会包含不同的阶段，例如图中所示的"测试"，"构建" 就是各个阶段。
图中的"Java代码扫描"， "Java安全扫描" 这种椭圆形的框则为任务，在流水线中支持配置串行任务，并行的任务
此页面具有如下功能：
    2.1 添加并行任务，当用户鼠标移动某个阶段后，则会在对应阶段下方显示"添加并行任务.png"，效果如图"添加并行任务"展示的效果
    2.2 添加串行任务，当用户鼠标移动到某个任务所在的连接线时，则会存在一个 + 号图标来添加串行任务。效果如图"添加串行任务.png"所示，用户可在此页面完成对CICD各个阶段的变更操作。
    2.3 当用户点击添加任务后，则会在右侧弹出对应可选择的管理任务的一栏，此处可选择很多不同类型的任务，效果如图"添加步骤.png"所示，用户可在此页面完成对CICD各个阶段的变更操作。
    2.4 当用户选中其中一个任务名称后，则会进入编辑界面，效果如图"管理任务页面.png"所示，并留意在图中箭头的部分，其也会在流水线中添加此任务名称

注意事项：
1. 请使用vue3的代码生成前端代码，而对于数据，可采用静态的模拟数据
2. 各任务之间是会有浅色线缆连接
3. 采用类似图中的效果类似画板的效果展示
4. 整体前端页面的风格以浅色系为主，当然也可直接类似提供的示例图的风格

